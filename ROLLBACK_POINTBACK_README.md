# 積分回饋 Rollback 功能實現說明

## 概述

本功能實現了對積分回饋系統的完整回滾機制，能夠撤銷 `do_pointback` 方法所做的所有資料庫變更，將系統狀態恢復到積分處理前的狀態。

## 功能特點

- **完整性**: 回滾所有受影響的資料表和記錄
- **原子性**: 使用資料庫事務確保操作的原子性
- **安全性**: 包含多重驗證機制防止誤操作
- **可追溯性**: 保留完整的操作記錄和錯誤處理

## 實現架構

### 核心方法

1. **`rollback_pointback(array $orderfomr_ids)`**
   - 主要入口點，處理多個訂單的回滾
   - 驗證訂單狀態，確保已處理過積分回饋
   - 協調整個回滾流程

2. **`get_pointback_records($orderfomr_id, $do_award_time)`**
   - 收集所有需要回滾的資料記錄
   - 涵蓋 10 個相關資料表的記錄查詢
   - 確保資料完整性和一致性

3. **`execute_rollback($rollback_data, $orderfomr_id)`**
   - 執行實際的資料庫回滾操作
   - 使用事務確保操作原子性
   - 按正確順序恢復各項資料

4. **`restore_point_increasable_value()`**
   - 重新計算增值積分現值
   - 確保系統財務數據的準確性

### HTTP 接口

- **路由**: `POST /order/rollback_pointback`
- **參數**: `id` (訂單 ID)
- **回應**: JSON 格式的成功/失敗訊息

## 涵蓋的資料表

| 資料表 | 操作類型 | 說明 |
|--------|----------|------|
| `points_record` | 刪除 | 移除積分變動記錄 |
| `point_increasable_record` | 刪除 | 移除增值積分記錄 |
| `increasing_limit_record` | 刪除 | 移除增值限額記錄 |
| `point_increasable_pool` | 刪除 | 移除資金池變動記錄 |
| `dividend_month_record` | 刪除 | 移除月分紅認列記錄 |
| `vip_type_relation` | 刪除+恢復 | 移除等級變更並恢復原等級 |
| `partner_level_relation` | 刪除 | 移除合夥人等級變更記錄 |
| `account` | 更新 | 恢復會員餘額和等級 |
| `bonus_setting` | 重新計算 | 恢復增值積分現值 |
| `orderform` | 更新 | 清除回饋處理標記 |
| `orderform_product` | 更新 | 清除供應商回饋標記 |

## 使用方法

### 透過 HTTP 接口

```bash
curl -X POST http://your-domain/order/rollback_pointback \
  -H "Content-Type: application/json" \
  -d '{"id": 123}'
```

### 直接調用方法

```php
use App\Services\pattern\OrderHelper;

// 回滾單個訂單
OrderHelper::rollback_pointback([123]);

// 回滾多個訂單
OrderHelper::rollback_pointback([123, 124, 125]);
```

### 使用測試腳本

```bash
php test_rollback_pointback.php 123
```

## 安全機制

1. **狀態驗證**: 確保訂單已處理過積分回饋
2. **時間戳驗證**: 使用 `do_award_time` 確保資料一致性
3. **事務保護**: 所有操作在資料庫事務中執行
4. **錯誤處理**: 完整的異常處理和錯誤訊息
5. **權限檢查**: 透過 Controller 層進行權限驗證

## 測試

提供了完整的測試腳本 `test_rollback_pointback.php`，包含：

- 原始狀態記錄
- 積分回饋執行
- 回滾操作執行
- 結果驗證
- 詳細的測試報告

## 注意事項

1. **不可逆操作**: 回滾操作會永久刪除相關記錄，請謹慎使用
2. **資料備份**: 建議在執行回滾前備份相關資料
3. **系統影響**: 回滾可能影響其他依賴積分系統的功能
4. **時序問題**: 確保在合適的時機執行回滾操作

## 檔案清單

- `app/Services/pattern/OrderHelper.php` - 核心實現
- `app/Http/Controllers/order/OrderCtrl.php` - HTTP 接口
- `test_rollback_pointback.php` - 測試腳本
- `ROLLBACK_POINTBACK_README.md` - 本說明文檔

## 版本資訊

- 實現日期: 2025-06-30
- 版本: 1.0.0
- 狀態: 已完成並測試
