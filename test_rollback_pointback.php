<?php

/**
 * 測試積分回饋 Rollback 功能
 *
 * 這個測試腳本用於驗證 rollback_pointback 功能的正確性
 * 請在執行前確保有適當的測試資料
 */

require_once __DIR__ . '/vendor/autoload.php';

use App\Services\pattern\OrderHelper;
use Illuminate\Support\Facades\DB;

class RollbackPointbackTest
{
    private $test_order_id;
    private $original_data = [];

    public function __construct($order_id)
    {
        $this->test_order_id = $order_id;
    }

    /**
     * 執行完整的測試流程
     */
    public function runTest()
    {
        echo "開始測試積分回饋 Rollback 功能...\n";

        try {
            // 1. 記錄原始狀態
            $this->recordOriginalState();
            echo "✓ 已記錄原始狀態\n";

            // 2. 執行積分回饋
            $this->executePointback();
            echo "✓ 已執行積分回饋\n";

            // 3. 記錄回饋後狀態
            $this->recordAfterPointbackState();
            echo "✓ 已記錄回饋後狀態\n";

            // 4. 執行 Rollback
            $this->executeRollback();
            echo "✓ 已執行 Rollback\n";

            // 5. 驗證 Rollback 結果
            $this->verifyRollbackResult();
            echo "✓ Rollback 驗證通過\n";

            echo "\n🎉 所有測試通過！Rollback 功能運作正常。\n";
        } catch (Exception $e) {
            echo "\n❌ 測試失敗: " . $e->getMessage() . "\n";
            throw $e;
        }
    }

    /**
     * 記錄原始狀態
     */
    private function recordOriginalState()
    {
        // 記錄訂單狀態
        $orderform = DB::connection('main_db')->table('orderform')->where('id', $this->test_order_id)->first();
        $this->original_data['orderform'] = $orderform;

        // 記錄訂單商品狀態
        $orderform_products = DB::connection('main_db')->table('orderform_product')
            ->where('orderform_id', $this->test_order_id)->get();
        $this->original_data['orderform_products'] = $orderform_products;

        // 記錄相關會員的積分狀態
        $user_id = $orderform->user_id;
        $account = DB::connection('main_db')->table('account')->where('id', $user_id)->first();
        $this->original_data['account'] = $account;

        // 記錄增值積分現值
        $bonus_setting = DB::connection('main_db')->table('bonus_setting')->where('id', 1)->first();
        $this->original_data['bonus_setting'] = $bonus_setting;

        // 記錄圓滿點數
        $this->original_data['increasing_limit_invest'] = $account->increasing_limit_invest;
        $this->original_data['increasing_limit_consumption'] = $account->increasing_limit_consumption;
        $this->original_data['increasing_limit_other'] = $account->increasing_limit_other;

        echo "原始狀態 - 訂單回饋時間: " . ($orderform->do_award_time ?: '未設定') . "\n";
        echo "原始狀態 - 會員現金積分: " . $account->point . "\n";
        echo "原始狀態 - 會員增值積分: " . $account->point_increasable . "\n";
        echo "原始狀態 - 功德圓滿點數: " . $account->increasing_limit_invest . "\n";
        echo "原始狀態 - 消費圓滿點數: " . $account->increasing_limit_consumption . "\n";
        echo "原始狀態 - 其他圓滿點數: " . $account->increasing_limit_other . "\n";
        echo "原始狀態 - 增值積分現值: " . $bonus_setting->value . "\n";
    }

    /**
     * 執行積分回饋
     */
    private function executePointback()
    {
        if ($this->original_data['orderform']->do_award_time) {
            throw new Exception("訂單已經處理過回饋，請使用未處理過的訂單進行測試");
        }

        OrderHelper::do_pointback([$this->test_order_id]);
    }

    /**
     * 記錄回饋後狀態
     */
    private function recordAfterPointbackState()
    {
        // 記錄回饋後的狀態
        $orderform = DB::connection('main_db')->table('orderform')->where('id', $this->test_order_id)->first();
        $this->original_data['after_pointback_orderform'] = $orderform;

        $user_id = $orderform->user_id;
        $account = DB::connection('main_db')->table('account')->where('id', $user_id)->first();
        $this->original_data['after_pointback_account'] = $account;

        $bonus_setting = DB::connection('main_db')->table('bonus_setting')->where('id', 1)->first();
        $this->original_data['after_pointback_bonus_setting'] = $bonus_setting;

        echo "回饋後狀態 - 訂單回饋時間: " . $orderform->do_award_time . "\n";
        echo "回饋後狀態 - 會員現金積分: " . $account->point . "\n";
        echo "回饋後狀態 - 會員增值積分: " . $account->point_increasable . "\n";
        echo "回饋後狀態 - 功德圓滿點數: " . $account->increasing_limit_invest . "\n";
        echo "回饋後狀態 - 消費圓滿點數: " . $account->increasing_limit_consumption . "\n";
        echo "回饋後狀態 - 其他圓滿點數: " . $account->increasing_limit_other . "\n";
        echo "回饋後狀態 - 增值積分現值: " . $bonus_setting->value . "\n";
    }

    /**
     * 執行 Rollback
     */
    private function executeRollback()
    {
        OrderHelper::rollback_pointback([$this->test_order_id]);
    }

    /**
     * 驗證 Rollback 結果
     */
    private function verifyRollbackResult()
    {
        // 檢查訂單狀態是否恢復
        $orderform = DB::connection('main_db')->table('orderform')->where('id', $this->test_order_id)->first();
        if ($orderform->do_award_time !== '') {
            throw new Exception("訂單回饋時間未正確清除");
        }

        // 檢查會員積分是否恢復
        $user_id = $orderform->user_id;
        $account = DB::connection('main_db')->table('account')->where('id', $user_id)->first();

        $original_point = (float)$this->original_data['account']->point;
        $current_point = (float)$account->point;
        if (abs($original_point - $current_point) > 0.00001) {
            throw new Exception("會員現金積分未正確恢復: 原始={$original_point}, 當前={$current_point}");
        }

        $original_point_increasable = (float)$this->original_data['account']->point_increasable;
        $current_point_increasable = (float)$account->point_increasable;
        if (abs($original_point_increasable - $current_point_increasable) > 0.00001) {
            throw new Exception("會員增值積分未正確恢復: 原始={$original_point_increasable}, 當前={$current_point_increasable}");
        }

        // 檢查圓滿點數是否恢復
        $original_increasing_limit_invest = (float)$this->original_data['increasing_limit_invest'];
        $current_increasing_limit_invest = (float)$account->increasing_limit_invest;
        if (abs($original_increasing_limit_invest - $current_increasing_limit_invest) > 0.00001) {
            throw new Exception("功德圓滿點數未正確恢復: 原始={$original_increasing_limit_invest}, 當前={$current_increasing_limit_invest}");
        }

        $original_increasing_limit_consumption = (float)$this->original_data['increasing_limit_consumption'];
        $current_increasing_limit_consumption = (float)$account->increasing_limit_consumption;
        if (abs($original_increasing_limit_consumption - $current_increasing_limit_consumption) > 0.00001) {
            throw new Exception("消費圓滿點數未正確恢復: 原始={$original_increasing_limit_consumption}, 當前={$current_increasing_limit_consumption}");
        }

        $original_increasing_limit_other = (float)$this->original_data['increasing_limit_other'];
        $current_increasing_limit_other = (float)$account->increasing_limit_other;
        if (abs($original_increasing_limit_other - $current_increasing_limit_other) > 0.00001) {
            throw new Exception("其他圓滿點數未正確恢復: 原始={$original_increasing_limit_other}, 當前={$current_increasing_limit_other}");
        }

        echo "驗證結果 - 訂單回饋時間: " . ($orderform->do_award_time ?: '已清除') . "\n";
        echo "驗證結果 - 會員現金積分: " . $account->point . " (原始: {$original_point})\n";
        echo "驗證結果 - 會員增值積分: " . $account->point_increasable . " (原始: {$original_point_increasable})\n";
        echo "驗證結果 - 功德圓滿點數: " . $account->increasing_limit_invest . " (原始: {$original_increasing_limit_invest})\n";
        echo "驗證結果 - 消費圓滿點數: " . $account->increasing_limit_consumption . " (原始: {$original_increasing_limit_consumption})\n";
        echo "驗證結果 - 其他圓滿點數: " . $account->increasing_limit_other . " (原始: {$original_increasing_limit_other})\n";
    }
}

// 使用範例
if (isset($argv[1])) {
    $order_id = (int)$argv[1];
    $test = new RollbackPointbackTest($order_id);
    $test->runTest();
} else {
    echo "使用方法: php test_rollback_pointback.php <order_id>\n";
    echo "範例: php test_rollback_pointback.php 123\n";
}
