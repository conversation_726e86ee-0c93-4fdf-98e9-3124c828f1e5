<?php

require_once __DIR__ . '/vendor/autoload.php';

// 載入 Laravel 應用程式
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Services\pattern\OrderHelper;

$order_id = $argv[1] ?? 1;

echo "測試 get_pointback_records 方法對訂單 {$order_id} 的記錄捕獲...\n\n";

// 取得訂單資訊
$orderform = \DB::connection('main_db')->table('orderform')->where('id', $order_id)->first();
if (!$orderform) {
    echo "找不到訂單 {$order_id}\n";
    exit;
}

$do_award_time = $orderform->do_award_time;
if (!$do_award_time) {
    echo "此訂單尚未處理回饋\n";
    exit;
}

echo "訂單回饋時間: {$do_award_time}\n\n";

// 使用 OrderHelper 的方法獲取記錄
$orderHelper = new OrderHelper();
$reflection = new ReflectionClass($orderHelper);
$method = $reflection->getMethod('get_pointback_records');
$method->setAccessible(true);

$records = $method->invoke($orderHelper, $order_id, $do_award_time);

echo "=== get_pointback_records 捕獲的記錄 ===\n\n";

echo "現金積分記錄數量: " . count($records['points_records']) . "\n";
foreach ($records['points_records'] as $record) {
    if (is_object($record)) {
        echo "  User: {$record->user_id}, Points: {$record->points}, Msg: {$record->msg}\n";
    } else {
        echo "  " . print_r($record, true) . "\n";
    }
}

echo "\n增值積分記錄數量: " . count($records['point_increasable_records']) . "\n";
foreach ($records['point_increasable_records'] as $record) {
    if (is_object($record)) {
        echo "  User: {$record->user_id}, Num: {$record->num}, Msg: {$record->msg}\n";
    } else {
        echo "  " . print_r($record, true) . "\n";
    }
}

echo "\n圓滿點數記錄數量: " . count($records['increasing_limit_records']) . "\n";
foreach ($records['increasing_limit_records'] as $record) {
    if (is_object($record)) {
        echo "  User: {$record->user_id}, Num: {$record->num}, Type: {$record->type}, Limit Type: {$record->limit_type}, Msg: {$record->msg}\n";
    } else {
        echo "  " . print_r($record, true) . "\n";
    }
}

echo "\n資金池記錄數量: " . count($records['pool_records']) . "\n";
foreach ($records['pool_records'] as $record) {
    echo "  CV: {$record->cv}, Msg: {$record->msg}\n";
}

echo "\n會員等級變更記錄數量: " . count($records['vip_relations']) . "\n";
foreach ($records['vip_relations'] as $record) {
    echo "  User: {$record->user_id}, From: {$record->vip_type_from} -> To: {$record->vip_type_to}, Msg: {$record->msg}\n";
}

echo "\n合夥人等級變更記錄數量: " . count($records['partner_relations']) . "\n";
foreach ($records['partner_relations'] as $record) {
    echo "  User: {$record->user_id}, Level: {$record->level_id}, Msg: {$record->msg}\n";
}
