<?php

require_once __DIR__ . '/vendor/autoload.php';

// 載入 Laravel 應用程式
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Services\pattern\OrderHelper;
use App\Services\Order\RollbackPointbackService;
use Illuminate\Support\Facades\DB;

$order_id = $argv[1] ?? 1;

echo "詳細測試訂單 {$order_id} 的回滾功能...\n\n";

// 取得訂單資訊
$orderform = DB::connection('main_db')->table('orderform')->where('id', $order_id)->first();
if (!$orderform || !$orderform->do_award_time) {
    echo "訂單未處理回饋或不存在\n";
    exit;
}

$do_award_time = $orderform->do_award_time;
$user_id = $orderform->user_id;

echo "訂單用戶: {$user_id}\n";
echo "回饋時間: {$do_award_time}\n\n";

// 記錄回滾前的狀態
echo "=== 回滾前狀態 ===\n";
$account_before = DB::connection('main_db')->table('account')->where('id', $user_id)->first();
echo "現金積分: {$account_before->point}\n";
echo "增值積分: {$account_before->point_increasable}\n";
echo "功德圓滿點數: {$account_before->increasing_limit_invest}\n";
echo "消費圓滿點數: {$account_before->increasing_limit_consumption}\n";
echo "其他圓滿點數: {$account_before->increasing_limit_other}\n";

// 檢查要回滾的記錄
$orderHelper = new OrderHelper();
$reflection = new ReflectionClass($orderHelper);
$method = $reflection->getMethod('get_pointback_records');
$method->setAccessible(true);
$records = $method->invoke($orderHelper, $order_id, $do_award_time);

echo "\n=== 要回滾的記錄統計 ===\n";
echo "現金積分記錄: " . count($records['points_records']) . " 筆\n";
echo "增值積分記錄: " . count($records['point_increasable_records']) . " 筆\n";
echo "圓滿點數記錄: " . count($records['increasing_limit_records']) . " 筆\n";

// 計算影響該用戶的記錄
$user_point_increasable_change = 0;
$user_increasing_limit_invest_change = 0;
$user_increasing_limit_consumption_change = 0;
$user_increasing_limit_other_change = 0;

foreach ($records['point_increasable_records'] as $record) {
    if ($record['user_id'] == $user_id) {
        $user_point_increasable_change += $record['num'];
    }
}

foreach ($records['increasing_limit_records'] as $record) {
    if ($record['user_id'] == $user_id) {
        if ($record['limit_type'] == 1) {
            $user_increasing_limit_invest_change += $record['num'];
        } elseif ($record['limit_type'] == 2) {
            $user_increasing_limit_consumption_change += $record['num'];
        } elseif ($record['limit_type'] == 3) {
            $user_increasing_limit_other_change += $record['num'];
        }
    }
}

echo "\n=== 該用戶的變更量 ===\n";
echo "增值積分變更: {$user_point_increasable_change}\n";
echo "功德圓滿點數變更: {$user_increasing_limit_invest_change}\n";
echo "消費圓滿點數變更: {$user_increasing_limit_consumption_change}\n";
echo "其他圓滿點數變更: {$user_increasing_limit_other_change}\n";

// 執行回滾
echo "\n=== 執行回滾 ===\n";
try {
    $rollbackService = new RollbackPointbackService();
    $rollbackService->rollbackPointback([$order_id]);
    echo "回滾執行完成\n";
} catch (Exception $e) {
    echo "回滾失敗: " . $e->getMessage() . "\n";
    exit;
}

// 記錄回滾後的狀態
echo "\n=== 回滾後狀態 ===\n";
$account_after = DB::connection('main_db')->table('account')->where('id', $user_id)->first();
echo "現金積分: {$account_after->point}\n";
echo "增值積分: {$account_after->point_increasable}\n";
echo "功德圓滿點數: {$account_after->increasing_limit_invest}\n";
echo "消費圓滿點數: {$account_after->increasing_limit_consumption}\n";
echo "其他圓滿點數: {$account_after->increasing_limit_other}\n";

// 計算實際變化
echo "\n=== 實際變化 ===\n";
$actual_point_change = $account_after->point - $account_before->point;
$actual_point_increasable_change = $account_after->point_increasable - $account_before->point_increasable;
$actual_increasing_limit_invest_change = $account_after->increasing_limit_invest - $account_before->increasing_limit_invest;
$actual_increasing_limit_consumption_change = $account_after->increasing_limit_consumption - $account_before->increasing_limit_consumption;
$actual_increasing_limit_other_change = $account_after->increasing_limit_other - $account_before->increasing_limit_other;

echo "現金積分實際變化: {$actual_point_change}\n";
echo "增值積分實際變化: {$actual_point_increasable_change} (預期: " . (-$user_point_increasable_change) . ")\n";
echo "功德圓滿點數實際變化: {$actual_increasing_limit_invest_change} (預期: " . (-$user_increasing_limit_invest_change) . ")\n";
echo "消費圓滿點數實際變化: {$actual_increasing_limit_consumption_change} (預期: " . (-$user_increasing_limit_consumption_change) . ")\n";
echo "其他圓滿點數實際變化: {$actual_increasing_limit_other_change} (預期: " . (-$user_increasing_limit_other_change) . ")\n";

// 檢查訂單狀態
$orderform_after = DB::connection('main_db')->table('orderform')->where('id', $order_id)->first();
echo "\n=== 訂單狀態 ===\n";
echo "回饋時間已清除: " . ($orderform_after->do_award_time ? '否' : '是') . "\n";
