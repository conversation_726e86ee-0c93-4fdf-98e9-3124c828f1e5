<?php

namespace Tests\Unit;

use PHPUnit\Framework\TestCase;
use App\Services\Order\RollbackPointbackService;
use App\Http\Controllers\order\OrderCtrl;
use Illuminate\Http\Request;
use Illuminate\Http\Exceptions\HttpResponseException;
use Mockery;

class OrderCtrlRollbackPointbackUnitTest extends TestCase
{
    private $mockRollbackService;

    protected function setUp(): void
    {
        parent::setUp();
        
        // 創建 RollbackPointbackService 的 mock
        $this->mockRollbackService = Mockery::mock(RollbackPointbackService::class);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /**
     * 捕獲 HttpResponseException 並返回響應數據
     */
    private function captureResponse(callable $callback): array
    {
        try {
            $callback();
            $this->fail('Expected HttpResponseException was not thrown');
            return []; // 這行永遠不會執行，但滿足返回類型要求
        } catch (HttpResponseException $e) {
            $response = $e->getResponse();
            return [
                'status_code' => $response->getStatusCode(),
                'content' => json_decode($response->getContent(), true)
            ];
        }
    }

    /**
     * 測試成功的回滾操作
     */
    public function test_rollback_pointback_success()
    {
        // 準備測試數據
        $orderformId = 123;
        $expectedResults = [
            [
                'order_id' => $orderformId,
                'success' => true,
                'deleted_records' => 15,
                'message' => '回滾成功'
            ]
        ];

        // 設定 mock 期望
        $this->mockRollbackService
            ->shouldReceive('rollbackPointback')
            ->once()
            ->with([$orderformId])
            ->andReturn($expectedResults);

        // 創建請求
        $request = Request::create('/order/order_ctrl/rollback_pointback', 'POST', [
            'id' => $orderformId
        ]);

        // 創建控制器實例，注入 mock 服務
        $controller = $this->createPartialMock(OrderCtrl::class, ['success', 'error']);
        
        // 使用反射設置私有屬性
        $reflection = new \ReflectionClass($controller);
        $property = $reflection->getProperty('rollbackService');
        $property->setAccessible(true);
        $property->setValue($controller, $this->mockRollbackService);

        // 設定 success 方法的期望
        $controller->expects($this->once())
            ->method('success')
            ->with(
                '取消回饋操作成功',
                ['rollback_results' => $expectedResults]
            );

        // 執行測試
        $controller->rollbackPointback($request);
    }

    /**
     * 測試缺少訂單ID的情況
     */
    public function test_rollback_pointback_missing_order_id()
    {
        // 創建沒有 id 參數的請求
        $request = Request::create('/order/order_ctrl/rollback_pointback', 'POST', []);

        // 創建控制器實例
        $controller = $this->createPartialMock(OrderCtrl::class, ['error']);
        
        // 使用反射設置私有屬性
        $reflection = new \ReflectionClass($controller);
        $property = $reflection->getProperty('rollbackService');
        $property->setAccessible(true);
        $property->setValue($controller, $this->mockRollbackService);

        // 設定 error 方法的期望
        $controller->expects($this->once())
            ->method('error')
            ->with('請提供訂單ID');

        // 確保服務沒有被調用
        $this->mockRollbackService->shouldNotReceive('rollbackPointback');

        // 執行測試
        $controller->rollbackPointback($request);
    }

    /**
     * 測試服務拋出異常的情況
     */
    public function test_rollback_pointback_service_exception()
    {
        // 準備測試數據
        $orderformId = 456;
        $exceptionMessage = '訂單不存在或未處理過回饋';

        // 設定 mock 拋出異常
        $this->mockRollbackService
            ->shouldReceive('rollbackPointback')
            ->once()
            ->with([$orderformId])
            ->andThrow(new \Exception($exceptionMessage));

        // 創建請求
        $request = Request::create('/order/order_ctrl/rollback_pointback', 'POST', [
            'id' => $orderformId
        ]);

        // 創建控制器實例
        $controller = $this->createPartialMock(OrderCtrl::class, ['error']);
        
        // 使用反射設置私有屬性
        $reflection = new \ReflectionClass($controller);
        $property = $reflection->getProperty('rollbackService');
        $property->setAccessible(true);
        $property->setValue($controller, $this->mockRollbackService);

        // 設定 error 方法的期望
        $controller->expects($this->once())
            ->method('error')
            ->with($exceptionMessage);

        // 執行測試
        $controller->rollbackPointback($request);
    }

    /**
     * 測試空字符串訂單ID
     */
    public function test_rollback_pointback_empty_order_id()
    {
        // 創建包含空字符串ID的請求
        $request = Request::create('/order/order_ctrl/rollback_pointback', 'POST', [
            'id' => ''
        ]);

        // 創建控制器實例
        $controller = $this->createPartialMock(OrderCtrl::class, ['error']);
        
        // 使用反射設置私有屬性
        $reflection = new \ReflectionClass($controller);
        $property = $reflection->getProperty('rollbackService');
        $property->setAccessible(true);
        $property->setValue($controller, $this->mockRollbackService);

        // 設定 error 方法的期望
        $controller->expects($this->once())
            ->method('error')
            ->with('請提供訂單ID');

        // 確保服務沒有被調用
        $this->mockRollbackService->shouldNotReceive('rollbackPointback');

        // 執行測試
        $controller->rollbackPointback($request);
    }

    /**
     * 測試無效的訂單ID格式
     */
    public function test_rollback_pointback_invalid_order_id()
    {
        // 準備測試數據
        $invalidId = 'invalid_id';
        $exceptionMessage = '無效的訂單ID';

        // 設定 mock 拋出異常
        $this->mockRollbackService
            ->shouldReceive('rollbackPointback')
            ->once()
            ->with([$invalidId])
            ->andThrow(new \Exception($exceptionMessage));

        // 創建請求
        $request = Request::create('/order/order_ctrl/rollback_pointback', 'POST', [
            'id' => $invalidId
        ]);

        // 創建控制器實例
        $controller = $this->createPartialMock(OrderCtrl::class, ['error']);
        
        // 使用反射設置私有屬性
        $reflection = new \ReflectionClass($controller);
        $property = $reflection->getProperty('rollbackService');
        $property->setAccessible(true);
        $property->setValue($controller, $this->mockRollbackService);

        // 設定 error 方法的期望
        $controller->expects($this->once())
            ->method('error')
            ->with($exceptionMessage);

        // 執行測試
        $controller->rollbackPointback($request);
    }

    /**
     * 測試成功回滾並返回詳細結果
     */
    public function test_rollback_pointback_with_detailed_results()
    {
        // 準備測試數據
        $orderformId = 789;
        $expectedResults = [
            [
                'order_id' => $orderformId,
                'success' => true,
                'deleted_records' => 25,
                'message' => '回滾成功',
                'details' => [
                    'points_records' => 5,
                    'point_increasable_records' => 3,
                    'increasing_limit_records' => 8,
                    'pool_records' => 2,
                    'dividend_records' => 1,
                    'vip_relations' => 4,
                    'partner_relations' => 2
                ]
            ]
        ];

        // 設定 mock 期望
        $this->mockRollbackService
            ->shouldReceive('rollbackPointback')
            ->once()
            ->with([$orderformId])
            ->andReturn($expectedResults);

        // 創建請求
        $request = Request::create('/order/order_ctrl/rollback_pointback', 'POST', [
            'id' => $orderformId
        ]);

        // 創建控制器實例
        $controller = $this->createPartialMock(OrderCtrl::class, ['success']);
        
        // 使用反射設置私有屬性
        $reflection = new \ReflectionClass($controller);
        $property = $reflection->getProperty('rollbackService');
        $property->setAccessible(true);
        $property->setValue($controller, $this->mockRollbackService);

        // 設定 success 方法的期望
        $controller->expects($this->once())
            ->method('success')
            ->with(
                '取消回饋操作成功',
                ['rollback_results' => $expectedResults]
            );

        // 執行測試
        $controller->rollbackPointback($request);
    }
}
