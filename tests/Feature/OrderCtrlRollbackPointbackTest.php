<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Services\Order\RollbackPointbackService;
use App\Http\Controllers\order\OrderCtrl;
use Illuminate\Http\Request;
use Illuminate\Http\Exceptions\HttpResponseException;
use Mockery;

class OrderCtrlRollbackPointbackTest extends TestCase
{

    private $mockRollbackService;
    private $controller;

    protected function setUp(): void
    {
        parent::setUp();

        // 創建 RollbackPointbackService 的 mock
        $this->mockRollbackService = Mockery::mock(RollbackPointbackService::class);

        // 創建控制器實例，注入 mock 服務
        $request = new Request();
        $this->controller = new OrderCtrl($request, $this->mockRollbackService);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /**
     * 捕獲 HttpResponseException 並返回響應數據
     */
    private function captureResponse(callable $callback): array
    {
        try {
            $callback();
            $this->fail('Expected HttpResponseException was not thrown');
            return []; // 這行永遠不會執行，但滿足返回類型要求
        } catch (HttpResponseException $e) {
            $response = $e->getResponse();
            return [
                'status_code' => $response->getStatusCode(),
                'content' => json_decode($response->getContent(), true)
            ];
        }
    }

    /**
     * 測試成功的回滾操作
     */
    public function test_rollback_pointback_success()
    {
        // 準備測試數據
        $orderformId = 123;
        $expectedResults = [
            [
                'order_id' => $orderformId,
                'success' => true,
                'deleted_records' => 15,
                'message' => '回滾成功'
            ]
        ];

        // 設定 mock 期望
        $this->mockRollbackService
            ->shouldReceive('rollbackPointback')
            ->once()
            ->with([$orderformId])
            ->andReturn($expectedResults);

        // 創建請求
        $request = Request::create('/order/order_ctrl/rollback_pointback', 'POST', [
            'id' => $orderformId
        ]);

        // 執行測試並捕獲響應
        $result = $this->captureResponse(function () use ($request) {
            $this->controller->rollbackPointback($request);
        });

        // 驗證回應
        $this->assertEquals(200, $result['status_code']);
        $this->assertEquals(1, $result['content']['code']); // success code
        $this->assertEquals('取消回饋操作成功', $result['content']['msg']);
        $this->assertEquals($expectedResults, $result['content']['data']['rollback_results']);
    }

    /**
     * 測試缺少訂單ID的情況
     */
    public function test_rollback_pointback_missing_order_id()
    {
        // 創建沒有 id 參數的請求
        $request = Request::create('/order/order_ctrl/rollback_pointback', 'POST', []);

        // 執行測試並捕獲響應
        $result = $this->captureResponse(function () use ($request) {
            $this->controller->rollbackPointback($request);
        });

        // 驗證回應
        $this->assertEquals(200, $result['status_code']); // Laravel 的 error 方法返回 200
        $this->assertEquals(0, $result['content']['code']); // error code
        $this->assertEquals('請提供訂單ID', $result['content']['msg']);

        // 確保服務沒有被調用
        $this->mockRollbackService->shouldNotHaveReceived('rollbackPointback');
    }

    /**
     * 測試服務拋出異常的情況
     */
    public function test_rollback_pointback_service_exception()
    {
        // 準備測試數據
        $orderformId = 456;
        $exceptionMessage = '訂單不存在或未處理過回饋';

        // 設定 mock 拋出異常
        $this->mockRollbackService
            ->shouldReceive('rollbackPointback')
            ->once()
            ->with([$orderformId])
            ->andThrow(new \Exception($exceptionMessage));

        // 創建請求
        $request = Request::create('/order/order_ctrl/rollback_pointback', 'POST', [
            'id' => $orderformId
        ]);

        // 執行測試並捕獲響應
        $result = $this->captureResponse(function () use ($request) {
            $this->controller->rollbackPointback($request);
        });

        // 驗證回應
        $this->assertEquals(200, $result['status_code']); // Laravel 的 error 方法返回 200
        $this->assertEquals(0, $result['content']['code']); // error code
        $this->assertEquals($exceptionMessage, $result['content']['msg']);
    }

    /**
     * 測試無效的訂單ID格式
     */
    public function test_rollback_pointback_invalid_order_id()
    {
        // 創建包含無效ID的請求
        $request = Request::create('/order/order_ctrl/rollback_pointback', 'POST', [
            'id' => 'invalid_id'
        ]);

        // 設定 mock 期望（即使ID無效，也會傳遞給服務）
        $this->mockRollbackService
            ->shouldReceive('rollbackPointback')
            ->once()
            ->with(['invalid_id'])
            ->andThrow(new \Exception('無效的訂單ID'));

        // 執行測試並捕獲響應
        $result = $this->captureResponse(function () use ($request) {
            $this->controller->rollbackPointback($request);
        });

        // 驗證回應
        $this->assertEquals(200, $result['status_code']);
        $this->assertEquals(0, $result['content']['code']); // error code
        $this->assertEquals('無效的訂單ID', $result['content']['msg']);
    }

    /**
     * 測試空字符串訂單ID
     */
    public function test_rollback_pointback_empty_order_id()
    {
        // 創建包含空字符串ID的請求
        $request = Request::create('/order/order_ctrl/rollback_pointback', 'POST', [
            'id' => ''
        ]);

        // 執行測試並捕獲響應
        $result = $this->captureResponse(function () use ($request) {
            $this->controller->rollbackPointback($request);
        });

        // 驗證回應
        $this->assertEquals(200, $result['status_code']);
        $this->assertEquals(0, $result['content']['code']); // error code
        $this->assertEquals('請提供訂單ID', $result['content']['msg']);

        // 確保服務沒有被調用
        $this->mockRollbackService->shouldNotHaveReceived('rollbackPointback');
    }

    /**
     * 測試成功回滾並返回詳細結果
     */
    public function test_rollback_pointback_with_detailed_results()
    {
        // 準備測試數據
        $orderformId = 789;
        $expectedResults = [
            [
                'order_id' => $orderformId,
                'success' => true,
                'deleted_records' => 25,
                'message' => '回滾成功',
                'details' => [
                    'points_records' => 5,
                    'point_increasable_records' => 3,
                    'increasing_limit_records' => 8,
                    'pool_records' => 2,
                    'dividend_records' => 1,
                    'vip_relations' => 4,
                    'partner_relations' => 2
                ]
            ]
        ];

        // 設定 mock 期望
        $this->mockRollbackService
            ->shouldReceive('rollbackPointback')
            ->once()
            ->with([$orderformId])
            ->andReturn($expectedResults);

        // 創建請求
        $request = Request::create('/order/order_ctrl/rollback_pointback', 'POST', [
            'id' => $orderformId
        ]);

        // 執行測試並捕獲響應
        $result = $this->captureResponse(function () use ($request) {
            $this->controller->rollbackPointback($request);
        });

        // 驗證回應
        $this->assertEquals(200, $result['status_code']);
        $this->assertEquals(1, $result['content']['code']); // success code
        $this->assertEquals('取消回饋操作成功', $result['content']['msg']);
        $this->assertEquals($expectedResults, $result['content']['data']['rollback_results']);

        // 驗證詳細結果
        $details = $result['content']['data']['rollback_results'][0]['details'];
        $this->assertEquals(5, $details['points_records']);
        $this->assertEquals(25, $result['content']['data']['rollback_results'][0]['deleted_records']);
    }
}
