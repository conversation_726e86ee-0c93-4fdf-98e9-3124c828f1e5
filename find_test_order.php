<?php

require_once __DIR__ . '/vendor/autoload.php';

// 載入 Laravel 應用程式
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use Illuminate\Support\Facades\DB;

echo "查找適合測試的訂單...\n\n";

// 先檢查所有訂單狀態
$all_orders = DB::connection('main_db')->table('orderform')
    ->limit(10)
    ->get(['id', 'user_id', 'status', 'do_award_time']);

echo "所有訂單狀態:\n";
foreach ($all_orders as $order) {
    echo "Order ID: {$order->id}, User ID: {$order->user_id}, Status: {$order->status}, Award Time: " . ($order->do_award_time ?: 'NULL') . "\n";
}

echo "\n";

// 查找未處理回饋的已完成訂單
$unprocessed_orders = DB::connection('main_db')->table('orderform')
    ->whereNull('do_award_time')
    ->where('status', 'Complete')
    ->limit(10)
    ->get(['id', 'user_id', 'status', 'do_award_time']);

echo "未處理回饋的已完成訂單:\n";
foreach ($unprocessed_orders as $order) {
    echo "Order ID: {$order->id}, User ID: {$order->user_id}, Status: {$order->status}\n";
}

echo "\n";

// 查找已處理回饋的訂單（用於測試回滾）
$processed_orders = DB::connection('main_db')->table('orderform')
    ->whereNotNull('do_award_time')
    ->where('status', 'Complete')
    ->orderBy('do_award_time', 'desc')
    ->limit(5)
    ->get(['id', 'user_id', 'status', 'do_award_time']);

echo "已處理回饋的訂單（可用於測試回滾）:\n";
foreach ($processed_orders as $order) {
    echo "Order ID: {$order->id}, User ID: {$order->user_id}, Status: {$order->status}, Award Time: {$order->do_award_time}\n";
}

echo "\n建議:\n";
if (count($unprocessed_orders) > 0) {
    $first_order = $unprocessed_orders->first();
    echo "1. 使用訂單 ID {$first_order->id} 進行完整測試（先執行回饋，再測試回滾）\n";
}

if (count($processed_orders) > 0) {
    $first_processed = $processed_orders->first();
    echo "2. 使用訂單 ID {$first_processed->id} 直接測試回滾功能\n";
}
