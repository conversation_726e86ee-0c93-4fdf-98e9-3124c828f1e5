<?php

require_once __DIR__ . '/vendor/autoload.php';

// 載入 Laravel 應用程式
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use Illuminate\Support\Facades\DB;

$order_id = $argv[1] ?? 851;

echo "檢查訂單 {$order_id} 的回饋記錄...\n\n";

// 取得訂單資訊
$orderform = DB::connection('main_db')->table('orderform')->where('id', $order_id)->first();
if (!$orderform) {
    echo "找不到訂單 {$order_id}\n";
    exit;
}

echo "訂單資訊:\n";
echo "ID: {$orderform->id}\n";
echo "User ID: {$orderform->user_id}\n";
echo "Status: {$orderform->status}\n";
echo "Award Time: " . ($orderform->do_award_time ?: 'NULL') . "\n\n";

$do_award_time = $orderform->do_award_time;

if (!$do_award_time) {
    echo "此訂單尚未處理回饋，無法檢查回饋記錄\n";
    exit;
}

// 檢查各種記錄
echo "=== 現金積分記錄 ===\n";
$points_records = DB::connection('main_db')->table('points_record')
    ->where('belongs_time', $do_award_time)
    ->get();
foreach ($points_records as $record) {
    echo "User: {$record->user_id}, Points: {$record->points}, Msg: {$record->msg}\n";
}

echo "\n=== 增值積分記錄 ===\n";
$point_increasable_records = DB::connection('main_db')->table('point_increasable_record')
    ->where('create_time', $do_award_time)
    ->get();
foreach ($point_increasable_records as $record) {
    echo "User: {$record->user_id}, Num: {$record->num}, Msg: {$record->msg}\n";
}

echo "\n=== 圓滿點數記錄 ===\n";
$increasing_limit_records = DB::connection('main_db')->table('increasing_limit_record')
    ->where('create_time', $do_award_time)
    ->get();
foreach ($increasing_limit_records as $record) {
    echo "User: {$record->user_id}, Num: {$record->num}, Type: {$record->type}, Limit Type: {$record->limit_type}, Msg: {$record->msg}\n";
}

echo "\n=== 資金池記錄 ===\n";
$pool_records = DB::connection('main_db')->table('point_increasable_pool')
    ->where('create_time', $do_award_time)
    ->get();
foreach ($pool_records as $record) {
    echo "CV: {$record->cv}, Msg: {$record->msg}\n";
}

echo "\n=== 會員等級變更記錄 ===\n";
$vip_relations = DB::connection('main_db')->table('vip_type_relation')
    ->where('create_time', $do_award_time)
    ->get();
foreach ($vip_relations as $record) {
    echo "User: {$record->user_id}, From: {$record->vip_type_from} -> To: {$record->vip_type_to}, Msg: {$record->msg}\n";
}

echo "\n=== 合夥人等級變更記錄 ===\n";
$partner_relations = DB::connection('main_db')->table('partner_level_relation')
    ->where('create_time', $do_award_time)
    ->get();
foreach ($partner_relations as $record) {
    echo "User: {$record->user_id}, Level: {$record->level_id}, Msg: {$record->msg}\n";
}
